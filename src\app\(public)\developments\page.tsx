import Header from '@/components/navigation/Header'

export default function DevelopmentsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Premium <span className="text-lime-600">Developments</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our exclusive collection of luxury residential and commercial developments
            </p>
          </div>

          {/* Featured Development */}
          <div className="bg-gradient-to-r from-lime-500 to-lime-600 rounded-2xl text-white p-8 mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <span className="bg-amber-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                  Now Selling
                </span>
                <h2 className="text-3xl font-bold mt-4 mb-4">Skyline Residences BGC</h2>
                <p className="text-lime-100 mb-6">
                  A 45-story luxury condominium tower in the heart of Bonifacio Global City, featuring world-class
                  amenities and stunning city views.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <div className="text-2xl font-bold">₱8.5M</div>
                    <div className="text-lime-200 text-sm">Starting Price</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">2025</div>
                    <div className="text-lime-200 text-sm">Completion</div>
                  </div>
                </div>
                <button className="bg-white text-lime-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Learn More
                </button>
              </div>
              <div className="bg-white/10 rounded-lg h-64 flex items-center justify-center">
                <span className="text-white/70">Featured Development Image</span>
              </div>
            </div>
          </div>

          {/* Development Categories */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="bg-lime-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-lime-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Residential</h3>
              <p className="text-gray-600">Luxury condominiums and exclusive residential communities</p>
            </div>
            <div className="text-center">
              <div className="bg-lime-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-lime-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Commercial</h3>
              <p className="text-gray-600">Prime office spaces and retail developments</p>
            </div>
            <div className="text-center">
              <div className="bg-lime-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-lime-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Mixed-Use</h3>
              <p className="text-gray-600">Integrated developments combining residential and commercial</p>
            </div>
          </div>

          {/* Development Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { name: 'Marina Bay Towers', location: 'Pasay City', status: 'Pre-Selling', price: '₱6.2M' },
              { name: 'Garden Heights', location: 'Quezon City', status: 'Ready for Occupancy', price: '₱4.8M' },
              { name: 'Business Park Plaza', location: 'Makati City', status: 'Under Construction', price: '₱12.5M' },
              { name: 'Sunset Residences', location: 'Alabang', status: 'Pre-Selling', price: '₱7.9M' },
              { name: 'Metro Central', location: 'Ortigas', status: 'Now Selling', price: '₱5.5M' },
              { name: 'Coastal Living', location: 'Paranaque', status: 'Pre-Selling', price: '₱8.8M' }
            ].map((development, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">{development.name} Image</span>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-semibold text-gray-900">{development.name}</h3>
                    <span
                      className={`text-xs font-semibold px-2 py-1 rounded ${
                        development.status === 'Ready for Occupancy'
                          ? 'bg-green-100 text-green-800'
                          : development.status === 'Now Selling'
                          ? 'bg-lime-100 text-lime-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {development.status}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-3">{development.location}</p>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-2xl font-bold text-lime-600">{development.price}</span>
                    <span className="text-sm text-gray-500">Starting price</span>
                  </div>
                  <button className="w-full bg-gray-900 text-white py-2 rounded-lg hover:bg-gray-800 transition-colors">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
