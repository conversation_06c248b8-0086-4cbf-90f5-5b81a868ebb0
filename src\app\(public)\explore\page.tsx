import Header from '@/components/navigation/Header'

export default function ExplorePage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Explore <span className="text-lime-600">Places</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the best neighborhoods and locations across the Philippines
            </p>
          </div>

          {/* Featured Location */}
          <div className="bg-gradient-to-r from-gray-900 to-gray-700 rounded-2xl text-white p-8 mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <span className="bg-lime-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured Location
                </span>
                <h2 className="text-3xl font-bold mt-4 mb-4">Bonifacio Global City</h2>
                <p className="text-gray-300 mb-6">
                  The Philippines&apos; premier business district, featuring modern infrastructure, world-class
                  shopping, dining, and entertainment options.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <div className="text-2xl font-bold">₱150K</div>
                    <div className="text-gray-400 text-sm">Avg. Price per sqm</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">250+</div>
                    <div className="text-gray-400 text-sm">Available Properties</div>
                  </div>
                </div>
                <button className="bg-lime-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-lime-600 transition-colors">
                  Explore BGC
                </button>
              </div>
              <div className="bg-white/10 rounded-lg h-64 flex items-center justify-center">
                <span className="text-white/70">BGC Skyline Image</span>
              </div>
            </div>
          </div>

          {/* Location Categories */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
              <div className="bg-lime-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-lime-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">Business Districts</h3>
              <p className="text-sm text-gray-600">Prime commercial areas</p>
            </div>
            <div className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
              <div className="bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">Residential</h3>
              <p className="text-sm text-gray-600">Family-friendly communities</p>
            </div>
            <div className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
              <div className="bg-yellow-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">Coastal</h3>
              <p className="text-sm text-gray-600">Beachfront properties</p>
            </div>
            <div className="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
              <div className="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">Educational</h3>
              <p className="text-sm text-gray-600">Near schools & universities</p>
            </div>
          </div>

          {/* Popular Locations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: 'Makati City',
                properties: 450,
                avgPrice: '₱120K/sqm',
                highlights: ['CBD', 'Shopping', 'Dining']
              },
              {
                name: 'Quezon City',
                properties: 680,
                avgPrice: '₱85K/sqm',
                highlights: ['Universities', 'Parks', 'Culture']
              },
              {
                name: 'Pasig City',
                properties: 320,
                avgPrice: '₱95K/sqm',
                highlights: ['Ortigas CBD', 'Malls', 'Transport']
              },
              { name: 'Alabang', properties: 280, avgPrice: '₱110K/sqm', highlights: ['Suburban', 'Golf', 'Schools'] },
              {
                name: 'Eastwood City',
                properties: 190,
                avgPrice: '₱100K/sqm',
                highlights: ['IT Hub', 'Nightlife', 'Modern']
              },
              {
                name: 'Rockwell',
                properties: 150,
                avgPrice: '₱180K/sqm',
                highlights: ['Luxury', 'Exclusive', 'Premium']
              }
            ].map((location, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="h-48 bg-gradient-to-br from-lime-400 to-lime-600 flex items-center justify-center">
                  <span className="text-white font-semibold text-lg">{location.name}</span>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{location.name}</h3>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-lg font-bold text-lime-600">{location.properties}</div>
                      <div className="text-sm text-gray-500">Properties</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-lime-600">{location.avgPrice}</div>
                      <div className="text-sm text-gray-500">Avg. Price</div>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {location.highlights.map((highlight, idx) => (
                        <span key={idx} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                          {highlight}
                        </span>
                      ))}
                    </div>
                  </div>
                  <button className="w-full bg-lime-600 text-white py-2 rounded-lg hover:bg-lime-700 transition-colors">
                    Explore Area
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
