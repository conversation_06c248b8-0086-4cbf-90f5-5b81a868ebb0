import { ReactNode } from 'react'

interface StatsCardProps {
  title: string
  value: string | number
  icon: ReactNode
  iconColor?: string
}

export default function StatsCard({ title, value, icon, iconColor = 'text-gray-500' }: StatsCardProps) {
  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 hover:shadow-md transition-shadow"
      role="region"
      aria-label={`${title} statistics`}
    >
      <div className="flex items-center">
        <div className="flex-shrink-0" aria-hidden="true">
          <div className={iconColor}>{icon}</div>
        </div>
        <div className="ml-3 sm:ml-4 min-w-0 flex-1">
          <p
            className="text-xs sm:text-sm font-medium text-gray-500 truncate"
            id={`${title.replace(/\s+/g, '-').toLowerCase()}-label`}
          >
            {title}
          </p>
          <p
            className="text-xl sm:text-2xl font-bold text-gray-900"
            aria-labelledby={`${title.replace(/\s+/g, '-').toLowerCase()}-label`}
          >
            {value}
          </p>
        </div>
      </div>
    </div>
  )
}
