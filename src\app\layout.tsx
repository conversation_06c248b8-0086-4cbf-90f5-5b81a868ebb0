import type { Metada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google'
import './globals.css'
import { NextAuthProvider } from '@/components/providers/sessionProvider'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin']
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin']
})

export const metadata: Metadata = {
  title: 'MRH Platform - Premium Real Estate in the Philippines',
  description:
    'Discover premium real estate opportunities in the Philippines most desirable locations. Find your dream property with MRH Platform.'
}

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <NextAuthProvider>{children}</NextAuthProvider>
      </body>
    </html>
  )
}
