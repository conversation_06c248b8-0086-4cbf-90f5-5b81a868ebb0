'use client'

import { useSession } from 'next-auth/react'
import Link from 'next/link'
import {
  HomeIcon,
  HeartIcon,
  ClockIcon,
  PlusIcon,
  ChartBarIcon,
  BellIcon,
  MapPinIcon
} from '@heroicons/react/24/outline'
import { StatsCard, QuickActionCard, ActivityItem } from '@/components/dashboard'

export default function DashboardPage() {
  const { data: session } = useSession()

  // Mock data - in a real app, this would come from your backend
  const userStats = {
    savedProperties: 12,
    viewedProperties: 45,
    inquiries: 8,
    alerts: 3
  }

  const recentActivity = [
    {
      id: 1,
      type: 'saved',
      title: 'Luxury Condo in BGC',
      location: 'Bonifacio Global City',
      price: '₱15,000,000',
      time: '2 hours ago'
    },
    {
      id: 2,
      type: 'inquiry',
      title: 'Modern House in Alabang',
      location: 'Muntinlupa City',
      price: '₱8,500,000',
      time: '1 day ago'
    },
    {
      id: 3,
      type: 'viewed',
      title: 'Penthouse in Makati',
      location: 'Makati City',
      price: '₱25,000,000',
      time: '2 days ago'
    }
  ]

  const quickActions = [
    {
      title: 'Search Properties',
      description: 'Find your dream property',
      href: '/properties',
      icon: HomeIcon,
      color: 'bg-lime-500 hover:bg-lime-600'
    },
    {
      title: 'List Property',
      description: 'List your property for sale',
      href: '/list-property',
      icon: PlusIcon,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      title: 'Market Analysis',
      description: 'View market trends',
      href: '/market-analysis',
      icon: ChartBarIcon,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      title: 'Explore Areas',
      description: 'Discover neighborhoods',
      href: '/explore',
      icon: MapPinIcon,
      color: 'bg-orange-500 hover:bg-orange-600'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'saved':
        return HeartIcon
      case 'inquiry':
        return BellIcon
      case 'viewed':
        return ClockIcon
      default:
        return HomeIcon
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'saved':
        return 'text-red-500'
      case 'inquiry':
        return 'text-blue-500'
      case 'viewed':
        return 'text-gray-500'
      default:
        return 'text-gray-500'
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      {/* Welcome Section */}
      <header className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {session?.user?.name || 'User'}!
        </h1>
        <p className="text-gray-600 text-sm sm:text-base">
          Here&apos;s what&apos;s happening with your real estate journey today.
        </p>
      </header>

      {/* Stats Cards */}
      <section className="mb-6 sm:mb-8" aria-labelledby="stats-heading">
        <h2 id="stats-heading" className="sr-only">
          Dashboard Statistics
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <StatsCard
            title="Saved Properties"
            value={userStats.savedProperties}
            icon={<HeartIcon className="h-8 w-8" />}
            iconColor="text-red-500"
          />
          <StatsCard
            title="Recently Viewed"
            value={userStats.viewedProperties}
            icon={<ClockIcon className="h-8 w-8" />}
            iconColor="text-blue-500"
          />
          <StatsCard
            title="Inquiries"
            value={userStats.inquiries}
            icon={<BellIcon className="h-8 w-8" />}
            iconColor="text-green-500"
          />
          <StatsCard
            title="Price Alerts"
            value={userStats.alerts}
            icon={<ChartBarIcon className="h-8 w-8" />}
            iconColor="text-purple-500"
          />
        </div>
      </section>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        {/* Quick Actions */}
        <section className="lg:col-span-1" aria-labelledby="quick-actions-heading">
          <h2 id="quick-actions-heading" className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
            Quick Actions
          </h2>
          <nav className="space-y-4" aria-label="Quick action navigation">
            {quickActions.map((action, index) => (
              <QuickActionCard
                key={index}
                title={action.title}
                description={action.description}
                href={action.href}
                icon={<action.icon className="h-6 w-6" />}
                color={action.color}
              />
            ))}
          </nav>
        </section>

        {/* Recent Activity */}
        <section className="lg:col-span-2" aria-labelledby="recent-activity-heading">
          <div className="flex items-center justify-between mb-4">
            <h2 id="recent-activity-heading" className="text-lg sm:text-xl font-semibold text-gray-900">
              Recent Activity
            </h2>
            <Link
              href="/activity"
              className="text-lime-600 hover:text-lime-700 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2 rounded"
              aria-label="View all recent activity"
            >
              View all
            </Link>
          </div>
          <div
            className="bg-white rounded-lg shadow-sm border border-gray-200"
            role="feed"
            aria-label="Recent property activity"
          >
            <div className="divide-y divide-gray-200">
              {recentActivity.map(activity => {
                const IconComponent = getActivityIcon(activity.type)
                return (
                  <ActivityItem
                    key={activity.id}
                    title={activity.title}
                    location={activity.location}
                    price={activity.price}
                    time={activity.time}
                    icon={<IconComponent className="h-6 w-6" />}
                    iconColor={getActivityColor(activity.type)}
                  />
                )
              })}
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
