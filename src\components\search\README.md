# Property Search Components

A comprehensive collection of reusable search components for the MRH Platform real estate application.

## Components Overview

### 1. LocationSearch

An intelligent location search component with autocomplete functionality for Philippine locations.

**Features:**

- Autocomplete with popular Philippine cities and areas
- Dropdown suggestions with location icons
- Keyboard navigation support
- Click outside to close functionality

**Usage:**

```tsx
import { LocationSearch } from '@/components/search'

;<LocationSearch value={location} onChange={value => setLocation(value)} placeholder="Enter city, area, or address" />
```

### 2. PropertyTypeSelector

A dropdown selector for different property types with icons and descriptions.

**Features:**

- 14 different property types (House, Condo, Commercial, etc.)
- Visual icons for each property type
- Detailed descriptions
- Keyboard navigation
- Selected state indication

**Usage:**

```tsx
import { PropertyTypeSelector } from '@/components/search'

;<PropertyTypeSelector value={propertyType} onChange={value => setPropertyType(value)} showIcons={true} />
```

### 3. PriceRangeFilter

Advanced price range filter with predefined ranges and custom range input.

**Features:**

- Separate ranges for sale vs rent properties
- Popular ranges section
- Custom range input
- Formatted price display (₱1M, ₱500K, etc.)
- Currency and listing type support

**Usage:**

```tsx
import { PriceRangeFilter } from '@/components/search'

;<PriceRangeFilter
  value={priceRange}
  onChange={value => setPriceRange(value)}
  listingType="sale" // or "rent"
  currency="₱"
/>
```

### 4. SearchButton

A customizable search button with loading states and animations.

**Features:**

- Multiple sizes (sm, md, lg)
- Multiple variants (primary, secondary, outline)
- Loading state with spinner
- Hover animations
- Full width option

**Usage:**

```tsx
import { SearchButton } from '@/components/search'

;<SearchButton onClick={handleSearch} loading={isLoading} size="lg" variant="primary" fullWidth={true}>
  Find Properties
</SearchButton>
```

### 5. PropertySearchForm

A comprehensive search form combining all search components with advanced filters.

**Features:**

- Sale/Rent toggle
- Basic search fields (location, type, price)
- Advanced filters (bedrooms, bathrooms, area)
- Multiple layout options
- Clear filters functionality
- Form validation

**Usage:**

```tsx
import { PropertySearchForm, SearchFilters } from '@/components/search'

const handleSearch = (filters: SearchFilters) => {
  console.log('Search filters:', filters)
  // Implement search logic
}

;<PropertySearchForm
  onSearch={handleSearch}
  layout="horizontal" // or "vertical", "compact"
  showAdvanced={true}
  initialFilters={{ listingType: 'sale' }}
/>
```

### 6. QuickSearch

A compact, expandable search component for headers and navigation.

**Features:**

- Expandable search input
- Smooth animations
- Auto-navigation to search results
- Customizable placeholder
- Search suggestions support

**Usage:**

```tsx
import { QuickSearch } from '@/components/search'

;<QuickSearch placeholder="Search properties..." onSearch={query => handleQuickSearch(query)} />
```

## SearchFilters Type

```tsx
interface SearchFilters {
  location: string
  propertyType: string
  priceRange: string
  listingType: 'sale' | 'rent'
  bedrooms?: string
  bathrooms?: string
  minArea?: string
  maxArea?: string
}
```

## Implementation Examples

### Hero Section Search

```tsx
import { PropertySearchForm } from '@/components/search'

;<PropertySearchForm onSearch={handleSearch} layout="horizontal" showAdvanced={false} />
```

### Properties Page Advanced Search

```tsx
import { PropertySearchForm } from '@/components/search'

;<PropertySearchForm
  onSearch={handleSearch}
  layout="compact"
  showAdvanced={true}
  initialFilters={{ listingType: 'sale' }}
/>
```

### Header Quick Search

```tsx
import { QuickSearch } from '@/components/search'

;<QuickSearch placeholder="Search properties..." className="hidden md:block" />
```

## Styling

All components use Tailwind CSS classes and follow the design system:

- Primary color: Lime Green (lime-500)
- Border radius: 8px (rounded-lg)
- Focus states: Lime ring
- Hover effects: Smooth transitions
- Responsive design: Mobile-first approach

## Accessibility

- Keyboard navigation support
- Focus management
- ARIA labels and roles
- Screen reader friendly
- High contrast support

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes
