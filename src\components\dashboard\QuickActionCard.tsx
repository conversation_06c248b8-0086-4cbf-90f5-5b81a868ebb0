import Link from 'next/link'
import { ReactNode } from 'react'

interface QuickActionCardProps {
  title: string
  description: string
  href: string
  icon: ReactNode
  color?: string
}

export default function QuickActionCard({
  title,
  description,
  href,
  icon,
  color = 'bg-gray-500 hover:bg-gray-600'
}: QuickActionCardProps) {
  return (
    <Link
      href={href}
      className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow group focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2"
      aria-label={`${title}: ${description}`}
    >
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-2 rounded-lg ${color} transition-colors`} aria-hidden="true">
          <div className="text-white">{icon}</div>
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-900 group-hover:text-lime-600 transition-colors">{title}</h3>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </Link>
  )
}
